// import Spinner from "@/components/Spinners/Spinner";
// import LocationSelect from "@/features/streets/components/LocationSelect";
// import StartStreets from "@/features/streets/components/StartStreets";
// import Streets from "@/features/streets/components/Streets";
// import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
// import { useLayoutEffect, useState } from "react";
// import { useGetCurrentMap } from "@/features/streets/api/useRoguelikeMutations";

export default function StreetsPage() {
    // Disable streets for now
    return null;
}

// export default function StreetsPage() {
//     const { isLoading, data: currentMap } = useGetCurrentMap();
//     const { data: currentUser } = useFetchCurrentUser();
//     const [roguelikeStarted, setRoguelikeStarted] = useState(false);

//     useLayoutEffect(() => {
//         if (currentMap && !currentMap?.mapComplete) {
//             setRoguelikeStarted(true);
//         } else {
//             setRoguelikeStarted(false);
//         }
//     }, [currentMap]);

//     return (
//         <>
//             {/* // Add skeleton here // */}
//             {isLoading ? null : (
//                 <>
//                     {roguelikeStarted ? (
//                         <Streets currentMap={currentMap} />
//                     ) : (
//                         <div className="h-full md:mt-24">
//                             <LocationSelect currentMap={currentMap} />
//                         </div>
//                     )}
//                 </>
//             )}
//         </>
//     );
// }
